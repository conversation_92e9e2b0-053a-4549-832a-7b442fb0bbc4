#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableHero
	{

		public static readonly string TName="Hero.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 英雄名称 
		/// </summary> 
		public int Name {get; set;}
		/// <summary> 
		/// 英雄描述 
		/// </summary> 
		public int Des {get; set;}
		/// <summary> 
		/// 是否启用 
		/// </summary> 
		public int Enable {get; set;}
		/// <summary> 
		/// 英雄icon 
		/// </summary> 
		public int Icon {get; set;}
		/// <summary> 
		/// 局内资源 
		/// </summary> 
		public int Res {get; set;}
		/// <summary> 
		/// spine资源 
		/// </summary> 
		public int Spine {get; set;}
		/// <summary> 
		/// spine缩放 
		/// </summary> 
		public int SpineScale {get; set;}
		/// <summary> 
		/// 详情页立绘 
		/// </summary> 
		public int Portrait {get; set;}
		/// <summary> 
		/// 品质 
		/// </summary> 
		public int QualityId {get; set;}
		/// <summary> 
		/// 职业 
		/// </summary> 
		public int HeroType {get; set;}
		/// <summary> 
		/// 站位 
		/// </summary> 
		public int HeroPosition {get; set;}
		/// <summary> 
		/// 阵营 
		/// </summary> 
		public int Camp {get; set;}
		/// <summary> 
		/// 获取途径 
		/// </summary> 
		public int Access {get; set;}
		#endregion

		public static TableHero GetData(int ID)
		{
			return TableManager.HeroData.Get(ID);
		}

		public static List<TableHero> GetAllData()
		{
			return TableManager.HeroData.GetAll();
		}

	}
	public sealed class TableHeroData
	{
		private Dictionary<int, TableHero> dict = new Dictionary<int, TableHero>();
		private List<TableHero> dataList = new List<TableHero>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableHero.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableHero>>(jsonContent);
			foreach (TableHero config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableHero Get(int id)
		{
			if (dict.TryGetValue(id, out TableHero item))
				return item;
			return null;
		}

		public List<TableHero> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
