#pragma warning disable CS0414 
//CS0414: The private field  is assigned but its value is never used 
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Game.Core
{
	public sealed partial class TableGameConfig
	{

		public static readonly string TName="GameConfig.json";

		#region 属性定义
		/// <summary> 
		/// ID 
		/// </summary> 
		public int ID {get; set;}
		/// <summary> 
		/// 转菊花持续时间 
		/// </summary> 
		public int WaitTime {get; set;}
		/// <summary> 
		/// client心跳间隔 
		/// </summary> 
		public float HeartBeatCheckTimer {get; set;}
		/// <summary> 
		/// client断网心跳检车次数 
		/// </summary> 
		public int OffLineCheckNum {get; set;}
		/// <summary> 
		/// client切后台后再返回强退游戏间隔(分钟) 
		/// </summary> 
		public int BackgroudToLoginUI {get; set;}
		/// <summary> 
		/// 弱网检测间隔 
		/// </summary> 
		public int WeakNetCheckInterval {get; set;}
		/// <summary> 
		/// 是否显示弱网菊花 
		/// </summary> 
		public int ShowWeakNetworkLoading {get; set;}
		/// <summary> 
		/// 最大重连次数 
		/// </summary> 
		public int MaxReconnecTimes {get; set;}
		/// <summary> 
		/// 重连时间间隔 
		/// </summary> 
		public int ReconnecIntervalTime {get; set;}
		/// <summary> 
		/// 断网后是否重连 
		/// </summary> 
		public int TryReconnect {get; set;}
		/// <summary> 
		/// Loading进入主界面超时时间 
		/// </summary> 
		public float ShowLoadingWaitTime {get; set;}
		/// <summary> 
		/// 子弹的之间的度值 
		/// </summary> 
		public float ZiDanAngleValue {get; set;}
		/// <summary> 
		/// 玩家的默认属性id 
		/// </summary> 
		public int PlayerBaseAttrId {get; set;}
		/// <summary> 
		/// 受击泛白的持续时间(单位秒) 
		/// </summary> 
		public float HurtContinueWhiteTime {get; set;}
		/// <summary> 
		/// 受击泛白的渐变时间 
		/// </summary> 
		public float HurtGradientTime {get; set;}
		/// <summary> 
		/// 失效：受击泛白的颜色 
		/// </summary> 
		public float[] HurtWhiteColor {get; set;}
		/// <summary> 
		/// 受击泛白的程度 
		/// </summary> 
		public float HurtWhiteColorMaxA {get; set;}
		/// <summary> 
		/// 受击缩放的比例实际是（1 + 0.2） 
		/// </summary> 
		public float HurtScaleValueF {get; set;}
		/// <summary> 
		/// 挂机系统最大时长，默认12小时。单位：秒 
		/// </summary> 
		public int HookMaxTime {get; set;}
		/// <summary> 
		/// 掉落的停留时间（帧） 
		/// </summary> 
		public int[] DropSceneStayTimeInt {get; set;}
		/// <summary> 
		/// 掉落的飞到角色身上的时间（帧） 
		/// </summary> 
		public int DropFlyPlayerTimeInt {get; set;}
		/// <summary> 
		/// 掉落飞向周围的时间(秒) 
		/// </summary> 
		public float DropFlyAroundTimeFloat {get; set;}
		/// <summary> 
		/// 掉落飞向周围的最小距离最大距离 
		/// </summary> 
		public float[] DropFlyAroundMinFMaxF {get; set;}
		/// <summary> 
		/// 玩家距离指定点的最小距离 
		/// </summary> 
		public float PlayerToPointMinDis {get; set;}
		/// <summary> 
		/// 逃跑的角度范围（偶数方便随机和取一半值） 
		/// </summary> 
		public int EscapeRandomMaxAngle {get; set;}
		/// <summary> 
		/// 技能的最小CD时间帧数 
		/// </summary> 
		public int SkillMinCDTimeValue {get; set;}
		/// <summary> 
		/// 角色出生点背面 
		/// </summary> 
		public float[] PlayerBornPositionXYZ {get; set;}
		/// <summary> 
		/// 角色出生点正面 
		/// </summary> 
		public float[] PlayerFrontBornPositionXYZ {get; set;}
		/// <summary> 
		/// 飘血的最大时间 
		/// </summary> 
		public float DamageBoardMaxTime {get; set;}
		/// <summary> 
		/// 副本开始 
		/// </summary> 
		public int[] StageBeginID {get; set;}
		/// <summary> 
		/// 挂机系统额外奖励实际获得倍数数组，分时段取倍数，几个数代表几个小时 
		/// </summary> 
		public string[] HookExtraAwardRatio {get; set;}
		/// <summary> 
		/// 伤害Z坐标信息 
		/// </summary> 
		public float DamageDepthPositionZ {get; set;}
		/// <summary> 
		/// 不在攻击范围以内玩家移动到目标的X的偏移 
		/// </summary> 
		public float TargetPointPositionDriftX {get; set;}
		/// <summary> 
		/// 血条变化灰色的处理信息延迟时间 
		/// </summary> 
		public float SceneBloodGreyDelayChangeTime {get; set;}
		/// <summary> 
		/// 血条变化灰色的处理信息变化的速度 
		/// </summary> 
		public float SceneBloodGreyChangeSpeed {get; set;}
		/// <summary> 
		/// 七日任务进度 
		/// </summary> 
		public int[] SevenDayTaskProgress {get; set;}
		/// <summary> 
		/// 七日任务阶段奖励 
		/// </summary> 
		public int[] SevenDayTaskStageReward {get; set;}
		/// <summary> 
		/// 七日任务持续时间 
		/// </summary> 
		public int SevenDayTaskDuration {get; set;}
		/// <summary> 
		/// 掉落飞到角色身上的特效 
		/// </summary> 
		public int DropScenePlayerEffectId {get; set;}
		/// <summary> 
		/// 掉落飞到角色播放特效的时间间隔 
		/// </summary> 
		public float DropScenePlayerEffectInterval {get; set;}
		/// <summary> 
		/// 主线任务引导小手消失任务 
		/// </summary> 
		public int MissionFingerVanish {get; set;}
		/// <summary> 
		/// 省电模式无操作进入时间，单位秒 
		/// </summary> 
		public int NoOperationEntryTime {get; set;}
		/// <summary> 
		/// 好友邀请奖励 
		/// </summary> 
		public int[] FriendRasinReward {get; set;}
		/// <summary> 
		/// 用户协议的URL 
		/// </summary> 
		public string UserAgreementURL {get; set;}
		/// <summary> 
		/// 各副本储存上限字段，各副本独立使用 
		/// </summary> 
		public int[] StorageLimit {get; set;}
		/// <summary> 
		/// 月卡首次购买直送礼包 
		/// </summary> 
		public int MonthCardFirstDrop {get; set;}
		/// <summary> 
		/// 月卡每日礼包 
		/// </summary> 
		public int MonthCardDailyDrop {get; set;}
		/// <summary> 
		/// 玩家初始头像 
		/// </summary> 
		public int PlayerInitHead {get; set;}
		/// <summary> 
		/// 史莱姆数量对应时间的系数（1,2,3,4） 
		/// </summary> 
		public float[] PlayerSlamNumTimeArgsArr {get; set;}
		/// <summary> 
		/// 聊天泡泡的时间显示时间和消失时间 
		/// </summary> 
		public float[] ChatBubbleShowTimeAndDisappearTime {get; set;}
		/// <summary> 
		/// 现金券数量阈值 
		/// </summary> 
		public int CashCouponThreshold {get; set;}
		/// <summary> 
		/// 隐私协议的URL 
		/// </summary> 
		public string PrivateAgreementURL {get; set;}
		/// <summary> 
		/// 只受1点伤害怪ID 
		/// </summary> 
		public int[] IsOnlyOneDamage {get; set;}
		/// <summary> 
		/// 怪物出生点和目标点的X的比例 
		/// </summary> 
		public float StartEndXScale {get; set;}
		/// <summary> 
		/// 怪物终点的Z或是怪物判断距离和终点的Z 
		/// </summary> 
		public float MonsterTargeEndZ {get; set;}
		/// <summary> 
		/// 伤害瓢字的参数信息 
		/// </summary> 
		public float[] DamageFlutterArgs {get; set;}
		/// <summary> 
		/// 城墙瓢字缩放 
		/// </summary> 
		public float DamageFlutWallScale {get; set;}
		/// <summary> 
		/// 玩家身上体力上限信息 
		/// </summary> 
		public int PowerHaveMaxNum {get; set;}
		/// <summary> 
		/// 体力恢复的时间间隔(单位秒) 
		/// </summary> 
		public int PowerAddTimeInterval {get; set;}
		/// <summary> 
		/// 挑战主线关卡消耗的体力 
		/// </summary> 
		public int MainStagePowerCost {get; set;}
		/// <summary> 
		/// 挑战主队玩法消耗的体力 
		/// </summary> 
		public int TeamUpPowerCost {get; set;}
		/// <summary> 
		/// 天气切换的小时（精确到秒） 
		/// </summary> 
		public int[] RefreshWeatherHour {get; set;}
		/// <summary> 
		/// 体力极限上限 
		/// </summary> 
		public int PowerLimitNum {get; set;}
		/// <summary> 
		/// 每波子弹数量（打多次子弹开始换弹） 
		/// </summary> 
		public int AttackSkillNum {get; set;}
		/// <summary> 
		/// 换弹时间，跟技能表cd读一样的逻辑，单独配置值 
		/// </summary> 
		public int AttackSkillChange {get; set;}
		/// <summary> 
		/// 先锋能量值上限 
		/// </summary> 
		public int VanguardEnergyLimit {get; set;}
		/// <summary> 
		/// 先锋值能量回复每N秒回复X点 
		/// </summary> 
		public string VanguardSkillRecovery {get; set;}
		/// <summary> 
		/// 机器人模型ID 
		/// </summary> 
		public int CyberModelID {get; set;}
		/// <summary> 
		/// 机器人坐标 
		/// </summary> 
		public float[] CyberBornPositionXYZ {get; set;}
		/// <summary> 
		/// 攻击几次播待机动画 
		/// </summary> 
		public int AttackNumPlayIdleAni {get; set;}
		/// <summary> 
		/// 主界面相机位置 
		/// </summary> 
		public float[] MainCameraPosition {get; set;}
		/// <summary> 
		/// 主界面相机角度 
		/// </summary> 
		public float[] MainCameraRotation {get; set;}
		/// <summary> 
		/// 主界面相机视角 
		/// </summary> 
		public float MainCameraView {get; set;}
		/// <summary> 
		/// 战斗界面相机角度 
		/// </summary> 
		public float[] BattleCameraPosition {get; set;}
		/// <summary> 
		/// 战斗界面相机角度 
		/// </summary> 
		public float[] BattleCameraRotation {get; set;}
		/// <summary> 
		/// 战斗界面相机视角 
		/// </summary> 
		public float BattleCameraView {get; set;}
		/// <summary> 
		/// 怪物移动速度比例参数 
		/// </summary> 
		public float MonsterMoveSpeedParm {get; set;}
		/// <summary> 
		/// 准心的最远距离 
		/// </summary> 
		public float AimPositionZ {get; set;}
		/// <summary> 
		/// 飘字距离随机阈值 
		/// </summary> 
		public float[] FloatingThreshold {get; set;}
		/// <summary> 
		/// 不需要打断音效的类型 
		/// </summary> 
		public int[] NotStopSoundTypes {get; set;}
		/// <summary> 
		/// 拍打能量值 
		/// </summary> 
		public int ButtEnergy {get; set;}
		/// <summary> 
		/// 点击屏幕出现手印的间隔 
		/// </summary> 
		public float ClickScreenInterval {get; set;}
		/// <summary> 
		/// 体力建筑每日刷新时间 
		/// </summary> 
		public int[] PowerRefreshTime {get; set;}
		/// <summary> 
		/// 体力建筑最大储存上限 
		/// </summary> 
		public int PowerStorageLimit {get; set;}
		/// <summary> 
		/// 体力过期时间（天） 
		/// </summary> 
		public int PowerPeriodValidity {get; set;}
		/// <summary> 
		/// 体力生成数量 
		/// </summary> 
		public int PowerNum {get; set;}
		/// <summary> 
		/// 可选角色 
		/// </summary> 
		public int[] OptionalPlayer {get; set;}
		/// <summary> 
		/// 挂机图纸 
		/// </summary> 
		public int DrawingDropGroupId {get; set;}
		/// <summary> 
		/// 挂机倍率 
		/// </summary> 
		public int[] DrawingTimes {get; set;}
		/// <summary> 
		/// 最大扫荡次数 
		/// </summary> 
		public int DrawingMaxTimes {get; set;}
		/// <summary> 
		/// 精英怪物随机权重 
		/// </summary> 
		public int[] EliteWeight {get; set;}
		/// <summary> 
		/// BOSS怪物随机权重 
		/// </summary> 
		public int[] BOSSWeight {get; set;}
		/// <summary> 
		/// 战斗内BUFF随机广告次数 
		/// </summary> 
		public int FightRandomAdvertisementNum {get; set;}
		/// <summary> 
		/// 昵称后缀区间 
		/// </summary> 
		public int[] NameNumInterval {get; set;}
		/// <summary> 
		/// 昵称最大字符限制 
		/// </summary> 
		public int NameMaxCharacter {get; set;}
		/// <summary> 
		/// 个性签名最大字符限制 
		/// </summary> 
		public int SignatureMaxCharacter {get; set;}
		/// <summary> 
		/// 新手第一组id 
		/// </summary> 
		public int PlayerStartNewGuildGroupId {get; set;}
		/// <summary> 
		/// 全服邮件的最大存储上限 
		/// </summary> 
		public int GlobalServerMailMaxNum {get; set;}
		/// <summary> 
		/// 推送：设置下线时挂机已满，铲子已满 间隔多长时间推送，单位：秒,第1个数为挂机间隔，第2个为铲子间隔 
		/// </summary> 
		public int[] PushFullIntervalTimes {get; set;}
		/// <summary> 
		/// 现金券转钻石比例 
		/// </summary> 
		public int CashCouponRatio {get; set;}
		/// <summary> 
		/// 活动列表排序（按照活动类型ActivityType排序） 
		/// </summary> 
		public int[] ActivityListSortConfig {get; set;}
		/// <summary> 
		/// 副本的广告上限 
		/// </summary> 
		public int[] StageDailyAdCount {get; set;}
		/// <summary> 
		/// 副本的消耗ID 
		/// </summary> 
		public int[] StageCostItemId {get; set;}
		/// <summary> 
		/// 副本的回复数量 
		/// </summary> 
		public int[] StageDailyGiveCount {get; set;}
		/// <summary> 
		/// 副本广告单次 
		/// </summary> 
		public int StageOneADGiveCount {get; set;}
		/// <summary> 
		/// 世界聊天冷却时间 
		/// </summary> 
		public int WorldChatInterval {get; set;}
		/// <summary> 
		/// 好友邀请奖励（填写邀请码） 
		/// </summary> 
		public int FriendInvitedReward {get; set;}
		/// <summary> 
		/// 好友礼物每日接收上限 
		/// </summary> 
		public int GiftRecMax {get; set;}
		/// <summary> 
		/// 抽卡系统：每天可观看广告次数上限 
		/// </summary> 
		public int GachaWatchMaxCount {get; set;}
		/// <summary> 
		/// 抽卡系统：每天可观看广告间隔时间，单位：秒 
		/// </summary> 
		public int GachaWatchInterTime {get; set;}
		/// <summary> 
		/// 抽卡系统：第一个是广告档位次数，第二个是500钻石档位，第三个是1500钻石档位，第四个无效 
		/// </summary> 
		public int[] GachaDrawRates {get; set;}
		/// <summary> 
		/// 抽卡系统：抽卡一次获得的经验 
		/// </summary> 
		public int GachaDrawExp {get; set;}
		/// <summary> 
		/// 抽卡系统：第一个是广告档位次数，第二个是500钻石档位，第三个是1500钻石档位，第四个无效 
		/// </summary> 
		public int[] GachaDrawCostDiamonds {get; set;}
		/// <summary> 
		/// 宠物抽卡系统：每天可观看广告间隔时间，单位：秒 
		/// </summary> 
		public int PetGachaWatchInterTime {get; set;}
		/// <summary> 
		/// 宠物抽卡系统：抽卡一次获得的经验 
		/// </summary> 
		public int PetGachaDrawExp {get; set;}
		/// <summary> 
		/// 圣物抽卡系统：观看广告可抽卡次数上限 
		/// </summary> 
		public int HallowsGachaAdvDrawMaxCount {get; set;}
		/// <summary> 
		/// 圣物抽卡系统：每天可观看广告次数上限 
		/// </summary> 
		public int HallowsGachaWatchMaxCount {get; set;}
		/// <summary> 
		/// 抽卡系统：观看广告可抽卡次数上限 
		/// </summary> 
		public int GachaAdvDrawMaxCount {get; set;}
		/// <summary> 
		/// 圣物抽卡系统：普通抽卡档位，下标为档位，值为次数，0档为广告初始次数 
		/// </summary> 
		public int[] HallowsGachaDrawRates {get; set;}
		/// <summary> 
		/// 系统赠送头像框 
		/// </summary> 
		public int PlayerDefaultHeadFrame {get; set;}
		/// <summary> 
		/// 邮件列表显示上限 
		/// </summary> 
		public int MailMaxNum {get; set;}
		/// <summary> 
		/// 问卷奖励邮件ID 
		/// </summary> 
		public int QuestionnaireEmail {get; set;}
		/// <summary> 
		/// 创建账号奖励ID 
		/// </summary> 
		public int NewaccountEmail {get; set;}
		/// <summary> 
		/// 第二天邮件奖励ID 
		/// </summary> 
		public int NextDayMailReward {get; set;}
		/// <summary> 
		/// 充值重复返还比例 
		/// </summary> 
		public int RechargeRatio {get; set;}
		/// <summary> 
		/// 日常任务ID 
		/// </summary> 
		public int[] DailyTaskArr {get; set;}
		/// <summary> 
		/// 周日常任务ID 
		/// </summary> 
		public int[] WeekTaskArr {get; set;}
		/// <summary> 
		/// 好友数量上限 
		/// </summary> 
		public int FriendMaxNumber {get; set;}
		/// <summary> 
		/// 可拉黑上限 
		/// </summary> 
		public int BlacklistMax {get; set;}
		/// <summary> 
		/// 好友送礼物品id|数量 
		/// </summary> 
		public int[] GiftItemNum {get; set;}
		/// <summary> 
		/// 玩家名字字符最大长度 
		/// </summary> 
		public int PlayerNameMaxLength {get; set;}
		/// <summary> 
		/// 好友礼物每日赠送上限 
		/// </summary> 
		public int GiftSendMax {get; set;}
		/// <summary> 
		/// GM工具发送邮件的过期时间，单位：天 
		/// </summary> 
		public int GMSendMailMaxDayTime {get; set;}
		/// <summary> 
		/// 创建角色时默认给玩家的钻石 
		/// </summary> 
		public int CreatePlayerDefaultDiamond {get; set;}
		/// <summary> 
		/// 创建角色时默认给玩家的金币 
		/// </summary> 
		public int CreatePlayerDefaultCoin {get; set;}
		/// <summary> 
		/// 玩家初始时装（使用称号时装第一个） 
		/// </summary> 
		public int PlayerInitDress {get; set;}
		/// <summary> 
		/// 主线关卡排名低于50%时，根据区间配置固定区间值和下面的增长值要一一对应。 
		/// </summary> 
		public int[] MainStagePassBounds {get; set;}
		/// <summary> 
		/// 主线关卡排名低于50%时，根据区间配置固定增长值。 
		/// </summary> 
		public int[] MainStagePassAdds {get; set;}
		/// <summary> 
		/// 邮件每日领取奖励 
		/// </summary> 
		public int[] DailyCollectEmail {get; set;}
		/// <summary> 
		/// 邮件每周领取奖励 
		/// </summary> 
		public int WeeklyReceiveEmail {get; set;}
		/// <summary> 
		/// 性别配置（保密|男|女） 
		/// </summary> 
		public string[] GenderConfig {get; set;}
		/// <summary> 
		/// 修改个人信息钻石配置 
		/// </summary> 
		public int ChangeInforCost {get; set;}
		/// <summary> 
		/// 个性签名修改时间限制（秒） 
		/// </summary> 
		public int SignatureChangeTime {get; set;}
		/// <summary> 
		/// 等级基金 
		/// </summary> 
		public int GradeFund {get; set;}
		/// <summary> 
		/// 公会名上限 
		/// </summary> 
		public int AllianceNameLen {get; set;}
		/// <summary> 
		/// 公会描述上限 
		/// </summary> 
		public int AllianceNoticeLen {get; set;}
		/// <summary> 
		/// 创建公会花费 
		/// </summary> 
		public int[] AllianceCreateExpend {get; set;}
		/// <summary> 
		/// 周卡对应礼包ID 
		/// </summary> 
		public int[] WeekCardGiftId {get; set;}
		/// <summary> 
		/// 每日好友申请数量上限 
		/// </summary> 
		public int FriendApplyMaxCount {get; set;}
		/// <summary> 
		/// 好友数量上限 
		/// </summary> 
		public int FriendMaxCount {get; set;}
		/// <summary> 
		/// 好友黑名单数量上限 
		/// </summary> 
		public int FriendBlackMaxCount {get; set;}
		/// <summary> 
		/// 对战匹配范围查找时间 
		/// </summary> 
		public int[] MainRankMatchTime {get; set;}
		/// <summary> 
		/// 对战每日胜利奖励次数 
		/// </summary> 
		public int MainBattleDailyWinTimes {get; set;}
		/// <summary> 
		/// 对战每日失败补给次数 
		/// </summary> 
		public int MainBattleDailyFailTimes {get; set;}
		/// <summary> 
		/// 对战初始杯数 
		/// </summary> 
		public int MainRankScoreInitial {get; set;}
		/// <summary> 
		/// 宝物抽取每日广告限次 
		/// </summary> 
		public int TreasureGachaAdTimes {get; set;}
		/// <summary> 
		/// 宝物抽取概率修正系数 
		/// </summary> 
		public int[] TreasureGachaProModify {get; set;}
		/// <summary> 
		/// 玩家上阵英雄数量 
		/// </summary> 
		public int HeroLineUpNum {get; set;}
		/// <summary> 
		/// 初始上阵英雄id 
		/// </summary> 
		public int[] HeroLineUpId {get; set;}
		/// <summary> 
		/// 英雄等级上限 
		/// </summary> 
		public int HeroLevelMax {get; set;}
		/// <summary> 
		/// 英雄觉醒上限 
		/// </summary> 
		public int HeroEvoMax {get; set;}
		#endregion

		public static TableGameConfig GetData(int ID)
		{
			return TableManager.GameConfigData.Get(ID);
		}

		public static List<TableGameConfig> GetAllData()
		{
			return TableManager.GameConfigData.GetAll();
		}

	}
	public sealed class TableGameConfigData
	{
		private Dictionary<int, TableGameConfig> dict = new Dictionary<int, TableGameConfig>();
		private List<TableGameConfig> dataList = new List<TableGameConfig>();


		public void Deserialize()
		{
			var configFilePath = Path.Combine("../configs/table", TableGameConfig.TName);
			string jsonContent = File.ReadAllText(configFilePath);
			var data = JsonConvert.DeserializeObject<List<TableGameConfig>>(jsonContent);
			foreach (TableGameConfig config in data)
			{
				dict.Add(config.ID, config);
				dataList.Add(config);
			}
		}

		public TableGameConfig Get(int id)
		{
			if (dict.TryGetValue(id, out TableGameConfig item))
				return item;
			return null;
		}

		public List<TableGameConfig> GetAll()
		{
			return dataList;
		}
	}
}
#pragma warning restore CS0414 
